export default [
  // 标题
  {
    options: {
      left: 200,
      top: 20,
      height: 30,
      width: 200,
      title: '家庭经济困难学生认定申请表',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 400,
      bottom: 50,
      vCenter: 300,
      hCenter: 35,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 基本情况表格边框
  {
    options: {
      left: 40,
      top: 60,
      height: 200,
      width: 520,
      coordinateSync: false,
      widthHeightSync: false,
      borderTop: 'solid',
      borderBottom: 'solid',
      borderLeft: 'solid',
      borderRight: 'solid',
      right: 560,
      bottom: 260,
      vCenter: 300,
      hCenter: 160,
    },
    printElementType: { title: '矩形', type: 'rect' },
  },

  // 基本情况 - 学号标签
  {
    options: {
      left: 50,
      top: 70,
      height: 20,
      width: 30,
      title: '学号',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 80,
      bottom: 90,
      vCenter: 65,
      hCenter: 80,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 学号输入框
  {
    options: {
      left: 80,
      top: 70,
      height: 20,
      width: 80,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 160,
      bottom: 90,
      vCenter: 120,
      hCenter: 80,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 姓名标签
  {
    options: {
      left: 180,
      top: 70,
      height: 20,
      width: 30,
      title: '姓名',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 210,
      bottom: 90,
      vCenter: 195,
      hCenter: 80,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 姓名输入框
  {
    options: {
      left: 210,
      top: 70,
      height: 20,
      width: 80,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 290,
      bottom: 90,
      vCenter: 250,
      hCenter: 80,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 性别标签
  {
    options: {
      left: 310,
      top: 70,
      height: 20,
      width: 30,
      title: '性别',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 340,
      bottom: 90,
      vCenter: 325,
      hCenter: 80,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 性别输入框
  {
    options: {
      left: 340,
      top: 70,
      height: 20,
      width: 50,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 390,
      bottom: 90,
      vCenter: 365,
      hCenter: 80,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 民族标签
  {
    options: {
      left: 50,
      top: 100,
      height: 20,
      width: 30,
      title: '民族',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 80,
      bottom: 120,
      vCenter: 65,
      hCenter: 110,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 民族输入框
  {
    options: {
      left: 80,
      top: 100,
      height: 20,
      width: 80,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 160,
      bottom: 120,
      vCenter: 120,
      hCenter: 110,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 院系标签
  {
    options: {
      left: 180,
      top: 100,
      height: 20,
      width: 30,
      title: '院系',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 210,
      bottom: 120,
      vCenter: 195,
      hCenter: 110,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 院系输入框
  {
    options: {
      left: 210,
      top: 100,
      height: 20,
      width: 80,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 290,
      bottom: 120,
      vCenter: 250,
      hCenter: 110,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 专业标签
  {
    options: {
      left: 310,
      top: 100,
      height: 20,
      width: 30,
      title: '专业',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 340,
      bottom: 120,
      vCenter: 325,
      hCenter: 110,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 专业输入框
  {
    options: {
      left: 340,
      top: 100,
      height: 20,
      width: 80,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 420,
      bottom: 120,
      vCenter: 380,
      hCenter: 110,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 年纪标签
  {
    options: {
      left: 50,
      top: 130,
      height: 20,
      width: 30,
      title: '年纪',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 80,
      bottom: 150,
      vCenter: 65,
      hCenter: 140,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 年纪输入框
  {
    options: {
      left: 80,
      top: 130,
      height: 20,
      width: 50,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 130,
      bottom: 150,
      vCenter: 105,
      hCenter: 140,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 班级标签
  {
    options: {
      left: 150,
      top: 130,
      height: 20,
      width: 30,
      title: '班级',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 180,
      bottom: 150,
      vCenter: 165,
      hCenter: 140,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 班级输入框
  {
    options: {
      left: 180,
      top: 130,
      height: 20,
      width: 80,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 260,
      bottom: 150,
      vCenter: 220,
      hCenter: 140,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 出生年月标签
  {
    options: {
      left: 280,
      top: 130,
      height: 20,
      width: 50,
      title: '出生年月',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 330,
      bottom: 150,
      vCenter: 305,
      hCenter: 140,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 出生年月输入框
  {
    options: {
      left: 330,
      top: 130,
      height: 20,
      width: 90,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 420,
      bottom: 150,
      vCenter: 375,
      hCenter: 140,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 手机号码标签
  {
    options: {
      left: 50,
      top: 160,
      height: 20,
      width: 50,
      title: '手机号码',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 100,
      bottom: 180,
      vCenter: 75,
      hCenter: 170,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 手机号码输入框
  {
    options: {
      left: 100,
      top: 160,
      height: 20,
      width: 100,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 200,
      bottom: 180,
      vCenter: 150,
      hCenter: 170,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 家庭人口标签
  {
    options: {
      left: 220,
      top: 160,
      height: 20,
      width: 50,
      title: '家庭人口',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 270,
      bottom: 180,
      vCenter: 245,
      hCenter: 170,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 家庭人口输入框
  {
    options: {
      left: 270,
      top: 160,
      height: 20,
      width: 50,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 320,
      bottom: 180,
      vCenter: 295,
      hCenter: 170,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 家庭困难类型标签
  {
    options: {
      left: 340,
      top: 160,
      height: 20,
      width: 80,
      title: '家庭困难类型',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 420,
      bottom: 180,
      vCenter: 380,
      hCenter: 170,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 是否办理贷款标签
  {
    options: {
      left: 50,
      top: 190,
      height: 20,
      width: 70,
      title: '是否办理贷款',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 120,
      bottom: 210,
      vCenter: 85,
      hCenter: 200,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 是否办理贷款输入框
  {
    options: {
      left: 120,
      top: 190,
      height: 20,
      width: 30,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 150,
      bottom: 210,
      vCenter: 135,
      hCenter: 200,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 身份证件号码标签
  {
    options: {
      left: 170,
      top: 190,
      height: 20,
      width: 80,
      title: '身份证件号码',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 250,
      bottom: 210,
      vCenter: 210,
      hCenter: 200,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 身份证件号码输入框
  {
    options: {
      left: 250,
      top: 190,
      height: 20,
      width: 170,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 420,
      bottom: 210,
      vCenter: 335,
      hCenter: 200,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 详细通讯地址标签
  {
    options: {
      left: 50,
      top: 220,
      height: 20,
      width: 80,
      title: '详细通讯地址',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 130,
      bottom: 240,
      vCenter: 90,
      hCenter: 230,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 详细通讯地址输入框
  {
    options: {
      left: 130,
      top: 220,
      height: 20,
      width: 290,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 420,
      bottom: 240,
      vCenter: 275,
      hCenter: 230,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 家庭成员情况标题
  {
    options: {
      left: 50,
      top: 280,
      height: 20,
      width: 80,
      title: '家庭成员情况',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 14,
      fontWeight: 'bold',
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 130,
      bottom: 300,
      vCenter: 90,
      hCenter: 290,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 家庭成员表格边框
  {
    options: {
      left: 40,
      top: 310,
      height: 120,
      width: 520,
      coordinateSync: false,
      widthHeightSync: false,
      borderTop: 'solid',
      borderBottom: 'solid',
      borderLeft: 'solid',
      borderRight: 'solid',
      right: 560,
      bottom: 430,
      vCenter: 300,
      hCenter: 370,
    },
    printElementType: { title: '矩形', type: 'rect' },
  },

  // 表头 - 姓名
  {
    options: {
      left: 50,
      top: 320,
      height: 20,
      width: 60,
      title: '姓名',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      right: 110,
      bottom: 340,
      vCenter: 80,
      hCenter: 330,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 表头 - 年龄
  {
    options: {
      left: 110,
      top: 320,
      height: 20,
      width: 50,
      title: '年龄',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      right: 160,
      bottom: 340,
      vCenter: 135,
      hCenter: 330,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 表头 - 与学生关系
  {
    options: {
      left: 160,
      top: 320,
      height: 20,
      width: 80,
      title: '与学生关系',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      right: 240,
      bottom: 340,
      vCenter: 200,
      hCenter: 330,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 表头 - 工作(学习)单位
  {
    options: {
      left: 240,
      top: 320,
      height: 20,
      width: 100,
      title: '工作(学习)单位',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      right: 340,
      bottom: 340,
      vCenter: 290,
      hCenter: 330,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 表头 - 职业
  {
    options: {
      left: 340,
      top: 320,
      height: 20,
      width: 60,
      title: '职业',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      right: 400,
      bottom: 340,
      vCenter: 370,
      hCenter: 330,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 表头 - 月收入
  {
    options: {
      left: 400,
      top: 320,
      height: 20,
      width: 60,
      title: '月收入',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      right: 460,
      bottom: 340,
      vCenter: 430,
      hCenter: 330,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 表头 - 健康状况
  {
    options: {
      left: 460,
      top: 320,
      height: 20,
      width: 80,
      title: '健康状况',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 540,
      bottom: 340,
      vCenter: 500,
      hCenter: 330,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 第一行数据 - 父亲姓名输入框
  {
    options: {
      left: 50,
      top: 350,
      height: 25,
      width: 60,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 110,
      bottom: 375,
      vCenter: 80,
      hCenter: 362.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第一行数据 - 父亲年龄输入框
  {
    options: {
      left: 110,
      top: 350,
      height: 25,
      width: 50,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 160,
      bottom: 375,
      vCenter: 135,
      hCenter: 362.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第一行数据 - 父亲关系
  {
    options: {
      left: 160,
      top: 350,
      height: 25,
      width: 80,
      title: '父亲',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      right: 240,
      bottom: 375,
      vCenter: 200,
      hCenter: 362.5,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 第一行数据 - 父亲工作单位输入框
  {
    options: {
      left: 240,
      top: 350,
      height: 25,
      width: 100,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 340,
      bottom: 375,
      vCenter: 290,
      hCenter: 362.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第一行数据 - 父亲职业输入框
  {
    options: {
      left: 340,
      top: 350,
      height: 25,
      width: 60,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 400,
      bottom: 375,
      vCenter: 370,
      hCenter: 362.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第一行数据 - 父亲月收入输入框
  {
    options: {
      left: 400,
      top: 350,
      height: 25,
      width: 60,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 460,
      bottom: 375,
      vCenter: 430,
      hCenter: 362.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第一行数据 - 父亲健康状况输入框
  {
    options: {
      left: 460,
      top: 350,
      height: 25,
      width: 80,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 540,
      bottom: 375,
      vCenter: 500,
      hCenter: 362.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第二行数据 - 母亲姓名输入框
  {
    options: {
      left: 50,
      top: 375,
      height: 25,
      width: 60,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 110,
      bottom: 400,
      vCenter: 80,
      hCenter: 387.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第二行数据 - 母亲年龄输入框
  {
    options: {
      left: 110,
      top: 375,
      height: 25,
      width: 50,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 160,
      bottom: 400,
      vCenter: 135,
      hCenter: 387.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第二行数据 - 母亲关系
  {
    options: {
      left: 160,
      top: 375,
      height: 25,
      width: 80,
      title: '母亲',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      right: 240,
      bottom: 400,
      vCenter: 200,
      hCenter: 387.5,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 第二行数据 - 母亲工作单位输入框
  {
    options: {
      left: 240,
      top: 375,
      height: 25,
      width: 100,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 340,
      bottom: 400,
      vCenter: 290,
      hCenter: 387.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第二行数据 - 母亲职业输入框
  {
    options: {
      left: 340,
      top: 375,
      height: 25,
      width: 60,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 400,
      bottom: 400,
      vCenter: 370,
      hCenter: 387.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第二行数据 - 母亲月收入输入框
  {
    options: {
      left: 400,
      top: 375,
      height: 25,
      width: 60,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 460,
      bottom: 400,
      vCenter: 430,
      hCenter: 387.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 第二行数据 - 母亲健康状况输入框
  {
    options: {
      left: 460,
      top: 375,
      height: 25,
      width: 80,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 540,
      bottom: 400,
      vCenter: 500,
      hCenter: 387.5,
    },
    printElementType: { title: '', type: 'text' },
  },

  // 申请理由标题
  {
    options: {
      left: 50,
      top: 450,
      height: 20,
      width: 60,
      title: '申请理由',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 14,
      fontWeight: 'bold',
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 110,
      bottom: 470,
      vCenter: 80,
      hCenter: 460,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 申请理由输入框
  {
    options: {
      left: 40,
      top: 480,
      height: 80,
      width: 520,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'left',
      textContentVerticalAlign: 'top',
      borderTop: 'solid',
      borderBottom: 'solid',
      borderLeft: 'solid',
      borderRight: 'solid',
      hideTitle: true,
      fontSize: 12,
      right: 560,
      bottom: 560,
      vCenter: 300,
      hCenter: 520,
    },
    printElementType: { title: '', type: 'longText' },
  },

  // 学院审议标题
  {
    options: {
      left: 50,
      top: 580,
      height: 20,
      width: 60,
      title: '学院审议',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 14,
      fontWeight: 'bold',
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 110,
      bottom: 600,
      vCenter: 80,
      hCenter: 590,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 学院审议边框
  {
    options: {
      left: 40,
      top: 610,
      height: 60,
      width: 520,
      coordinateSync: false,
      widthHeightSync: false,
      borderTop: 'solid',
      borderBottom: 'solid',
      borderLeft: 'solid',
      borderRight: 'solid',
      right: 560,
      bottom: 670,
      vCenter: 300,
      hCenter: 640,
    },
    printElementType: { title: '矩形', type: 'rect' },
  },

  // 学院审核人标签
  {
    options: {
      left: 350,
      top: 630,
      height: 20,
      width: 50,
      title: '审核人：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 400,
      bottom: 650,
      vCenter: 375,
      hCenter: 640,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 学院审核时间标签
  {
    options: {
      left: 450,
      top: 630,
      height: 20,
      width: 80,
      title: '审核时间：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 530,
      bottom: 650,
      vCenter: 490,
      hCenter: 640,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 学校审议标题
  {
    options: {
      left: 50,
      top: 690,
      height: 20,
      width: 60,
      title: '学校审议',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 14,
      fontWeight: 'bold',
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 110,
      bottom: 710,
      vCenter: 80,
      hCenter: 700,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 学校审议边框
  {
    options: {
      left: 40,
      top: 720,
      height: 60,
      width: 520,
      coordinateSync: false,
      widthHeightSync: false,
      borderTop: 'solid',
      borderBottom: 'solid',
      borderLeft: 'solid',
      borderRight: 'solid',
      right: 560,
      bottom: 780,
      vCenter: 300,
      hCenter: 750,
    },
    printElementType: { title: '矩形', type: 'rect' },
  },

  // 学校审核人标签
  {
    options: {
      left: 350,
      top: 740,
      height: 20,
      width: 50,
      title: '审核人：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 400,
      bottom: 760,
      vCenter: 375,
      hCenter: 750,
    },
    printElementType: { title: '文本', type: 'text' },
  },

  // 学校审核时间标签
  {
    options: {
      left: 450,
      top: 740,
      height: 20,
      width: 80,
      title: '审核时间：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 12,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 530,
      bottom: 760,
      vCenter: 490,
      hCenter: 750,
    },
    printElementType: { title: '文本', type: 'text' },
  },
];
