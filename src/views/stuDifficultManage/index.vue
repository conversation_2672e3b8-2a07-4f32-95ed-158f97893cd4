<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <!-- 按钮组区 -->
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()"> 提名 </a-button>

            <a-button type="default" preIcon="icon-ym icon-ym-btn-upload" @click="handleImport"> 导入 </a-button>
            <a-button type="default" preIcon="icon-ym icon-ym-btn-download" @click="handleExport"> 导出 </a-button>
            <a-button type="default" preIcon="icon-ym icon-ym-delete" @click="handelBatchRemove"> 删除 </a-button>
            <a-button type="default" preIcon="icon-ym icon-ym-report-icon-preview-print" @click="handlePrint"> 批量打印 </a-button>
            <a-button type="default" preIcon="icon-ym icon-ym-report-icon-preview-print" @click="handlePrintTotal"> 汇总表打印 </a-button>
          </template>
          <!-- <template #toolbar>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()"> 设置 </a-button>
          </template> -->
          <template #bodyCell="{ column, record }">
            <!-- 审核状态列 -->
            <template v-if="column.key === 'shzt'">
              <MtcnTextTag :content="getFlowStatusContent(record.status)" :color="getFlowStatusColor(record.shzt)" />
            </template>
            <!-- 日期列格式化 -->
            <template v-else-if="column.dataIndex === 'sqsj' || column.dataIndex === 'creatorTime'">
              {{ formatDate(record[column.dataIndex]) }}
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" :dropDownActions="[]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <!-- 新增/申请表 -->
    <AddForm @register="registerAddPopup" @reload="reload" />
    <!-- 问卷结果 -->
    <QuestionForm @register="registerQuestionForm" />
    <!-- 导出模态框 -->
    <ExportModal @register="registerExportModal" />
    <!-- 导入模态框 -->
    <ImportModal @register="registerImportModal" @reload="reload" />
    <!-- 打印选择模态框 -->
    <PrintSelect @register="registerPrintSelect" @change="handleShowBrowse" />
    <!-- 打印预览模态框 -->
    <PrintBrowse @register="registerPrintBrowse" />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useMessage, useConfirm } from '@/hooks/web/useMessage';
  import { BasicTable, useTable, BasicColumn, TableAction } from '@/components/Table';
  import { BasicModal, useModalInner, useModal } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import AddForm from './components/AddForm.vue';
  import QuestionForm from '@/views/stuDifficult/components/QuestionForm.vue';
  import { ImportModal } from '@/components/CommonModal';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import PrintSelect from '@/components/PrintDesign/printSelect/index.vue';
  import PrintBrowse from '@/components/PrintDesign/printBrowse/index.vue';
  import { Icon } from '@/components/Icon';
  import { useDefineSetting } from '@/hooks/setting/useDefineSetting';
  import * as schoolApi from '@/api/school';
  const baseStore = useBaseStore();
  const api = useBaseApi('/api/knsPdxx');
  const { createMessage, createConfirm } = useMessage();
  const { flowStatusList, flowUrgentList, getUrgentText, getUrgentTextColor, getFlowStatusContent, getFlowStatusColor } = useDefineSetting();

  const columns: BasicColumn[] = [
    {
      title: '学号',
      dataIndex: 'xsbh',
      width: 120,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '姓名',
      dataIndex: 'xm',
      width: 100,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '院系',
      dataIndex: 'dwmc',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '专业',
      dataIndex: 'zymc',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '班级',
      dataIndex: 'bjmc',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '现在年级',
      dataIndex: 'xznj',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '申请时间',
      dataIndex: 'sqsj',
      width: 150,
      resizable: true,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '评定学年',
      dataIndex: 'pdxn',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '申请困难等级',
      dataIndex: 'sqknlx',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '评定困难等级',
      dataIndex: 'knlxmc',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '是否有效',
      dataIndex: 'sfyx',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '民族',
      dataIndex: 'mzdm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '身份证件类型',
      dataIndex: 'sfzjlxdm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '身份证件号',
      dataIndex: 'sfzjh',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '出生地',
      dataIndex: 'csddm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '国家地区',
      dataIndex: 'gjdqdm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '港澳台侨',
      dataIndex: 'gatqdm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '政治面貌',
      dataIndex: 'zzmmdm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '信仰宗教',
      dataIndex: 'xyzjdm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '婚姻状态',
      dataIndex: 'hyzkdm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '生源地',
      dataIndex: 'syddm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '培养层次',
      dataIndex: 'pyccdm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '手机号',
      dataIndex: 'sjh',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '电子信箱',
      dataIndex: 'dzxx',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '联系电话',
      dataIndex: 'lxdh',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: 'QQ号',
      dataIndex: 'qqh',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '学籍状态',
      dataIndex: 'xjztdm',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '审核状态',
      dataIndex: 'shzt',
      width: 100,
      resizable: true,
      ellipsis: true,
      fixed: 'right',
      onFilter: (value: string, record: Recordable) => record.shzt.includes(value),
    },
  ];

  const [registerTable, { reload, getSelectRowKeys, clearSelectedRowKeys, getFetchParams }] = useTable({
    api: params => api.getList({ params }),
    columns,

    rowSelection: {
      type: 'checkbox',
    },
    useSearchForm: true,
    formConfig: getFormConfig(),
    showTableSetting: true,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
  });
  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '申请表',
        onClick: () => {
          addOrUpdateHandle(record);
        },
      },
      {
        label: '问卷结果',
        onClick: () => {
          openQuestionForm(true, { ...record, opType: 2 });
        },
      },
    ];
  }
  function getFormConfig() {
    return {
      labelWidth: 100,
      schemas: [
        {
          field: 'keyword',
          label: '关键词',
          component: 'Input',
          componentProps: {
            placeholder: '请输入学号、姓名等关键词搜索',
            submitOnPressEnter: true,
          },
        },
        {
          field: 'dwdm',
          label: '院系',
          component: 'Select',
          componentProps: {
            options: [],
          },
        },
        {
          field: 'xn',
          label: '评定学年',
          component: 'Select',
          componentProps: {
            options: [],
          },
        },
      ],
    };
  }

  // 日期格式化
  function formatDate(dateString: string) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  }

  // 批量删除
  async function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) {
      createMessage.error('请至少选择一条数据');
      return;
    }
    createConfirm({
      title: '确认删除',
      content: '是否确认删除选中的困难生信息?',
      onOk: async () => {
        try {
          const { msg } = await api.batchRemove({ params: { ids: ids.join(',') } });
          createMessage.success(msg || '删除成功');
          clearSelectedRowKeys();
          reload();
        } catch (error) {
          console.error('批量删除失败:', error);
          createMessage.error('批量删除失败');
        }
      },
    });
  }

  // 导出
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  function handleExport() {
    const listQuery = getFetchParams();
    openExportModal(true, {
      listQuery,
      exportType: 'KnsPdxxExport',
      apiUrl: '/api/knsPdxx/export',
    });
  }

  // 导入
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  function handleImport() {
    openImportModal(true, {
      type: 'knsPdxx',
      isUploadDirectly: true,
      actionUrl: '/api/knsPdxx/importPdxx',
      downloadTemplateUrl: '/api/knsPdxx/downloadTemplate',
    });
  }

  //#region 打印
  const [registerPrintSelect, { openModal: openPrintSelect }] = useModal();
  const [registerPrintBrowse, { openModal: openPrintBrowse }] = useModal();
  const printIds = ref([]);

  const recoPrintId = ref('');
  function handlePrint() {
    const rowKeys = getSelectRowKeys();
    if (!rowKeys.length) return createMessage.error('请选择要打印的数据');
    printIds.value = [recoPrintId.value];
    if (printIds.value?.length === 1) return handleShowBrowse(rowKeys.map(o => ({ formId: o })));
    openPrintSelect(true, printIds.value);
  }
  function handleShowBrowse(ids, psnBh, recoId) {
    printIds.value = [recoPrintId.value];
    openPrintBrowse(true, {
      id: printIds.value[0],
      formInfo: ids,
    });
  }
  //#endregion

  // 汇总表打印
  function handlePrintTotal() {
    createMessage.info('汇总表打印功能开发中');
  }

  // 新增/申请表
  const [registerAddPopup, { openPopup: openAddPopup }] = usePopup();
  function addOrUpdateHandle(record) {
    openAddPopup(true, record);
  }
  // 问卷结果
  const [registerQuestionForm, { openPopup: openQuestionForm }] = usePopup();

  async function getOptions() {
    // 加载学院数据
    schoolApi.getXY({ pageSize: 99999 }).then(res => {
      getForm().updateSchema({ field: 'dwdm', componentProps: { options: res.data.list, fieldNames: { label: 'dwBzmc', value: 'dwDm' } } });
    });

    // 加载学年数据
    const academicYears = await baseStore.getDictionaryData('xn');
    getForm().updateSchema({ field: 'xn', componentProps: { options: academicYears, fieldNames: { label: 'fullName', value: 'enCode' } } });
  }
  onMounted(() => {
    getOptions();
  });
</script>
