<template>
  <BasicPopup
    v-bind="$attrs"
    title="公示模板设置"
    showOkBtn
    okText="保存"
    cancelText="关闭"
    @register="registerPopup"
    class="full-popup"
    @close="handleClose"
    @ok="handleSubmit">
    <div class="setting-box p-20px">
      <div class="left-box">
        <h2 class="title mt-10px">困难生公示</h2>
        <hr class="mt-10px mb-10px" />
        <div class="sub-title mb-20px">
          <div v-if="fieldValue.hjrs">人数：6人</div>
          <div v-if="fieldValue.ggsj">公式日期：2025.05.01~2025.10.01</div>
          <div v-if="fieldValue.llcs">浏览次数：100次</div>
        </div>
        <BasicTable @register="registerTable" v-if="fieldValue.yszs == '0'"> </BasicTable>
        <template v-else>
          <div class="content-box mt-20px">
            <div class="text-content">
              <div class="award-level">低保户困难生（<span>3人</span>）：</div>
              <div class="name-list">
                <span>张三（2021001001）</span>
                <span>赵六（2021004001）</span>
                <span>李明（2021006001）</span>
              </div>
            </div>
            <div class="text-content">
              <div class="award-level">残疾困难生（<span>2人</span>）：</div>
              <div class="name-list">
                <span>李四（2021002001）</span>
                <span>钱七（2021005001）</span>
              </div>
            </div>
            <div class="text-content">
              <div class="award-level">五保户困难生（<span>1人</span>）：</div>
              <div class="name-list">
                <span>王五（2021003001）</span>
              </div>
            </div>
          </div>
        </template>
      </div>
      <div class="right-box">
        <div class="point mb-10px">公式页面信息设置</div>
        <BasicForm @register="registerForm"> </BasicForm>
      </div>
    </div>
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { ref, computed, reactive, toRefs, nextTick, watch, unref } from 'vue';
  import { BasicPopup, usePopup, usePopupInner } from '@/components/Popup';
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { ScrollContainer } from '@/components/Container';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/kns/gssz');
  const { createMessage, createConfirm } = useMessage();
  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);
  const selectedOption = ref(1);
  const fieldValue = reactive({
    hjrs: 1,
    ggsj: 1,
    llcs: 1,
    nrxs: [],
    yszs: '0',
  });

  // 表格选项配置
  const tableOptions = [
    { fullName: '姓名', id: 'xm' },
    { fullName: '学号', id: 'xsbh' },
    { fullName: '困难类型', id: 'pddjmc' },
    { fullName: '学院', id: 'dwmc' },
    { fullName: '专业', id: 'zymc' },
    { fullName: '班级', id: 'bjmc' },
  ];

  // 根据options生成columns的方法
  const generateColumns = (options: any[]): BasicColumn[] => {
    return options.map(option => ({
      title: option.fullName,
      dataIndex: option.id,
      resizable: true,
      ellipsis: true,
      width: 120, // 设置默认宽度，避免DragHandle组件接收到undefined
      ifShow: ({ row }) => {
        return fieldValue?.nrxs?.includes(option.id);
      },
    }));
  };

  const columns: BasicColumn[] = generateColumns(tableOptions);
  const state = reactive({
    type: '0',
  });

  const [registerTable, { reload, getForm, getFetchParams, setColumns, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    dataSource: [
      { pddjmc: '五保户', dwmc: '计算机学院', xsbh: '2021001001', xm: '张三', zymc: '计算机科学与技术', bjmc: '计科2101班' },
      { pddjmc: '残疾', dwmc: '电子工程学院', xsbh: '2021002001', xm: '李四', zymc: '电子信息工程', bjmc: '电信2101班' },
      { pddjmc: '孤儿', dwmc: '机械工程学院', xsbh: '2021003001', xm: '王五', zymc: '机械设计制造及其自动化', bjmc: '机械2101班' },
      { pddjmc: '低保户', dwmc: '外国语学院', xsbh: '2021004001', xm: '赵六', zymc: '英语', bjmc: '英语2101班' },
    ],
    columns,
    showTableSetting: false,
  });
  const [registerForm, { setFieldsValue, resetFields, validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'yszs',
        label: '样式展示',
        component: 'Radio',
        componentProps: {
          options: [
            {
              fullName: '列表式',
              id: '0',
            },
            {
              fullName: '文本式',
              id: '1',
            },
          ],
          onChange: async (e, value) => {
            fieldValue.yszs = e;
            if (e == '1') {
              setTimeout(() => {
                setColumns(generateColumns(tableOptions));
              }, 1000);
            }
          },
        },
      },
      {
        ifShow: () => fieldValue.yszs == '0',
        field: 'nrxs',
        label: '内容显示',
        component: 'Checkbox',
        componentProps: {
          options: tableOptions,
          onChange: async (e, value) => {
            fieldValue.nrxs = e;
            if (fieldValue.yszs == '1') {
              setColumns(generateColumns(tableOptions));
            }
          },
        },
      },
      {
        field: 'llcs',
        label: '浏览次数',
        component: 'Switch',
        defaultValue: 1,
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onChange: e => {
            fieldValue.llcs = e;
          },
        },
      },
      {
        field: 'hjrs',
        label: '人数',
        component: 'Switch',
        defaultValue: 1,
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onChange: e => {
            fieldValue.hjrs = e;
          },
        },
      },
      {
        field: 'ggsj',
        label: '公示时间',
        component: 'Switch',
        defaultValue: 1,
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onChange: e => {
            fieldValue.ggsj = e;
          },
        },
      },
    ],
    layout: 'vertical',
  });
  const emit = defineEmits(['register', 'reload']);

  /**
   * 初始化函数
   *
   * @param data 初始化数据
   */
  async function init(data) {
    try {
      // 如果有ID，获取详情；否则获取默认配置
      const res = data.id ? await api.request('get', `/${data.id}`) : await api.request('get', '/getOne');

      if (res.data) {
        const configData = res.data;
        configData.nrxs = !configData.nrxs ? [] : JSON.parse(configData.nrxs || '[]');
        setFieldsValue(configData);
        fieldValue.nrxs = configData.nrxs;
        fieldValue.yszs = configData.yszs || '0';
        fieldValue.ggsj = configData.ggsj;
        fieldValue.hjrs = configData.hjrs;
        fieldValue.llcs = configData.llcs;
        nextTick(() => {
          setColumns(generateColumns(tableOptions));
        });
      }
    } catch (error) {
      console.error('获取公示设置失败:', error);
      createMessage.error('获取公示设置失败');
    }
  }
  async function handleSubmit() {
    try {
      const values = await validate();
      changeOkLoading(true);

      const submitData = {
        ...values,
        nrxs: JSON.stringify(values.nrxs || []),
      };

      const res = values.id ? await api.edit({ data: submitData, params: { id: values.id } }) : await api.save({ data: submitData });

      createMessage.success(res.msg || '保存成功');
      changeOkLoading(false);
      emit('reload');
      setTimeout(() => {
        closePopup();
      }, 200);
    } catch (error) {
      console.error('保存公示设置失败:', error);
      createMessage.error('保存公示设置失败');
      changeOkLoading(false);
    }
  }
  const handleClose = () => {
    closePopup();
  };
</script>
<style scoped lang="less">
  .setting-box {
    height: 100%;
    display: grid;
    grid-template-columns: minmax(100px, 1fr) 400px;
    gap: 20px;

    .right-box {
      padding: 0 20px;
      border-left: 1px solid #dddddd;
    }
    .left-box {
      height: calc(100% - 40px);
      .content-box {
        display: flex;
        flex-direction: column;
        gap: 20px;
        .text-content {
          margin-bottom: 16px;
          .award-level {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
            span {
              font-size: 16px;
              color: #1890ff;
              font-weight: 600;
            }
          }
          .name-list {
            padding-left: 20px;
            line-height: 1.8;
            span {
              display: inline-block;
              margin-right: 20px;
              color: #666;
              font-size: 14px;
            }
          }
        }
      }

      .title {
        font-size: 20px;
        font-weight: 500;
        text-align: center;
      }
      .sub-title {
        display: grid;
        grid-template-columns: 1fr 3fr 1fr;
        gap: 20px;
        text-align: center;
      }
    }
  }
</style>
