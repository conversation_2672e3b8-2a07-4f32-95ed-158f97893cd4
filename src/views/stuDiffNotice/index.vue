<!--
 * @Description: 困难生公示
 * @Autor: Fhz
 * @Date: 2025-03-03 11:04:45
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 10:30:40
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-flow-node-system-task" @click="openSettingPopup(true, {})">公示模板设置</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <ExportModal @register="registerExportModal" />
    <SettingPopup @register="registerPopup" />
    <DetailPopup @register="registerDetailPopup" />
  </div>
</template>

<script lang="ts" setup>
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import { useModal } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  import { useMessage } from '@/hooks/web/useMessage';
  // import { getList, exportList } from '@/api/studentGrant/studentGrantNotice';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import SettingPopup from './SettingPopup.vue';
  import DetailPopup from './DetailPopup.vue';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const indexApi = useBaseApi('/api/kns/gslljl');
  const { createMessage } = useMessage();
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  const [registerPopup, { openPopup: openSettingPopup }] = usePopup();
  const [registerDetailPopup, { openPopup: openDetailPopup }] = usePopup();

  const columns: BasicColumn[] = [
    {
      title: '公示标题',
      dataIndex: 'gsTitle',
      width: 500,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '公示人数',
      dataIndex: 'hjrs',
      width: 100,
      align: 'right',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '公示时间',
      dataIndex: 'gssj',
      width: 180,
      format: 'date|YYYY-MM-DD',
      resizable: true,
      ellipsis: true,
    },
  ];

  const [registerTable, { reload }] = useTable({
    api: params => indexApi.getList({ params }),
    columns,
    useSearchForm: true,
    beforeFetch: params => {
      return params;
    },
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: '关键字',
          component: 'Input',
          componentProps: {
            placeholder: '请输入关键字搜索',
            submitOnPressEnter: true,
          },
        },
      ],
    },
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function getTableActions(record): any[] {
    return [
      {
        label: '详情',
        onClick: () => openDetailPopup(true, { id: record.knbzdm, ...record }),
      },
      // {
      //   label: '导出名单',
      //   onClick: () => handleExport(record.id),
      // },
    ];
  }

  function addOrUpdateHandle() {
    openFormModal(true);
  }

  function handleExport() {
    openExportModal(true);
  }
</script>
