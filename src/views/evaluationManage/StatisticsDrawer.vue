<template>
  <Drawer v-bind="$attrs" title="填写统计" width="600" @close="handleOk">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'count'">
          <span @click="handleOk" class="count-color">{{ record.count }}</span>
        </template>
      </template>
    </BasicTable>
  </Drawer>
</template>
<script lang="ts" setup>
  import { Drawer } from 'ant-design-vue';
  import { BasicTable, useTable, BasicColumn } from '@/components/Table';
  import * as schoolApi from '@/api/school';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import { useBaseStore } from '@/store/modules/base';
  import { onMounted } from 'vue';

  // 初始化API
  const api2 = useBaseApi('/api/knsMzpy');
  const baseStore = useBaseStore();
  const statColumns: BasicColumn[] = [
    { title: '民主评议单位', dataIndex: 'dwmc', resizable: true, ellipsis: true, width: 200 },
    { title: '填写数量', dataIndex: 'tbrs', resizable: true, ellipsis: true, width: 100, align: 'right' },
  ];

  const [registerTable, { getForm }] = useTable({
    api: params => api2.request('get', '/api/knsMzpy/statisticsList', { params, isFullPath: true }),
    columns: statColumns,
    showIndexColumn: true,
    showTableSetting: false,
    useSearchForm: true,
    formConfig: {
      labelWidth: 120,
      schemas: [
        {
          field: 'dwdm',
          label: '院系',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            options: [],
            fieldNames: { label: 'dwBzmc', value: 'dwDm' },
          },
        },
        {
          field: 'xn',
          label: '学年',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            options: [],
            fieldNames: { label: 'fullName', value: 'enCode' },
          },
        },
      ],
    },
  });
  const emits = defineEmits(['handleOk']);
  const handleOk = record => {
    emits('handleOk', record);
  };
  async function getOptions() {
    schoolApi.getXY({ pageSize: 99999 }).then(res => {
      getForm().updateSchema({ field: 'dwdm', componentProps: { options: res.data.list, fieldNames: { label: 'dwBzmc', value: 'dwDm' } } });
    });

    // 加载学年数据
    const academicYears = await baseStore.getDictionaryData('xn');
    getForm().updateSchema({ field: 'xn', componentProps: { options: academicYears, fieldNames: { label: 'fullName', value: 'enCode' } } });
  }
  onMounted(() => {
    nextTick(() => {
      getOptions();
    });
  });
</script>
<style scoped lang="less">
  .count-color {
    color: @primary-color;
    cursor: pointer;
  }
</style>
