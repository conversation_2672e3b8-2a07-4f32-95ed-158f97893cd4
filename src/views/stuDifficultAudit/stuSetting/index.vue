<!--
 * @Description: 困难生设置
 * @Autor: Fhz
 * @Date: 2025-04-24 11:32:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 13:45:53
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center bg-white">
      <div class="mtcn-basic-popup-header">
        <div class="mtcn-basic-popup-header-left">
          <span class="mtcn-basic-title">困难生设置</span>
        </div>
        <div class="mtcn-basic-popup-header__toolbar" v-if="activeKey == 1">
          <!-- <a-button type="primary" @click="save()">保存</a-button> -->
          <a-button class="m-l-10px">批量失效</a-button>
        </div>
      </div>
      <a-tabs v-model:activeKey="activeKey" destroyInactiveTabPane>
        <a-tab-pane key="1">
          <template #tab>
            <div class="pl-10px pr-10px">业务设置</div>
          </template>
        </a-tab-pane>
        <a-tab-pane key="2" tab="困难生类型"> </a-tab-pane>
        <a-tab-pane key="3" tab="家庭困难类型"> </a-tab-pane>
      </a-tabs>
      <Business ref="baseCom" v-if="activeKey == 1" />
      <StuDifficultType v-if="activeKey == 2" />
      <FamilyDifficultType v-if="activeKey == 3" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import Business from './components/business/index.vue';
  import StuDifficultType from './components/stuDifficult/index.vue';
  import FamilyDifficultType from './components/familyDiffcultType/index.vue';
  const activeKey = ref('1');

  const baseCom = ref(null);
  function save() {
    baseCom.value.handleSubmit();
  }
</script>

<style lang="less" scoped></style>
